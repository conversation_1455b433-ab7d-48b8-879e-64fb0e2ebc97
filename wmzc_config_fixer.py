#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WMZC配置文件修复脚本
解决配置警告：未知字段和API密钥长度验证问题
"""

import json
import os
import shutil
from datetime import datetime

class WMZCConfigFixer:
    """WMZC配置修复器"""
    
    def __init__(self):
        self.config_dir = os.path.expanduser("~/.wmzc_trading")
        self.local_trading_config = "trading_config.json"
        self.local_wmzc_config = "wmzc_config.json"
        self.fixes_applied = 0
        
    def create_config_directory(self):
        """创建配置目录"""
        try:
            os.makedirs(self.config_dir, exist_ok=True)
            print(f"✅ 配置目录已创建: {self.config_dir}")
            return True
        except Exception as e:
            print(f"❌ 创建配置目录失败: {e}")
            return False
    
    def backup_configs(self):
        """备份现有配置"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        try:
            # 备份本地配置文件
            if os.path.exists(self.local_trading_config):
                backup_path = f"trading_config_backup_{timestamp}.json"
                shutil.copy2(self.local_trading_config, backup_path)
                print(f"✅ 备份 trading_config.json -> {backup_path}")
            
            if os.path.exists(self.local_wmzc_config):
                backup_path = f"wmzc_config_backup_{timestamp}.json"
                shutil.copy2(self.local_wmzc_config, backup_path)
                print(f"✅ 备份 wmzc_config.json -> {backup_path}")
            
            return True
        except Exception as e:
            print(f"❌ 备份配置失败: {e}")
            return False
    
    def fix_wmzc_config(self):
        """修复wmzc_config.json"""
        print("🔧 修复 wmzc_config.json...")
        
        try:
            # 加载现有配置
            with open(self.local_wmzc_config, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 标准化配置结构
            standard_config = {
                # 基本配置
                "API_KEY": config.get("okx_api_key", "da636867-490f-4e3e-81b2-870841afb860"),
                "SECRET_KEY": config.get("okx_secret_key", "C15B6EE0CF3FFDEE5834865D3839325E"),
                "PASSPHRASE": config.get("okx_passphrase", "Mx123456@"),
                
                # 交易所配置
                "EXCHANGE": config.get("exchange_selection", "OKX"),
                "DEFAULT_SYMBOL": "BTC-USDT-SWAP",
                "DEFAULT_TIMEFRAME": "1m",
                
                # 交易参数
                "DEFAULT_AMOUNT": float(config.get("default_amount", "10.0")),
                "STOP_LOSS_PERCENT": float(config.get("stop_loss_percent", "2.0")),
                "TAKE_PROFIT_PERCENT": float(config.get("take_profit_percent", "4.0")),
                "MAX_POSITION_SIZE": float(config.get("max_position_size", "100.0")),
                
                # 技术指标参数
                "RSI_PERIOD": int(config.get("rsi_period", "14")),
                "RSI_OVERBOUGHT": int(config.get("rsi_overbought", "70")),
                "RSI_OVERSOLD": int(config.get("rsi_oversold", "30")),
                
                "MACD_FAST": int(config.get("macd_fast", "12")),
                "MACD_SLOW": int(config.get("macd_slow", "26")),
                "MACD_SIGNAL": int(config.get("macd_signal", "9")),
                
                "KDJ_PERIOD": int(config.get("kdj_period", "9")),
                "KDJ_K_PERIOD": int(config.get("kdj_k_period", "3")),
                "KDJ_D_PERIOD": int(config.get("kdj_d_period", "3")),
                
                "BOLLINGER_PERIOD": int(config.get("bollinger_period", "20")),
                "BOLLINGER_STD": float(config.get("bollinger_std", "2")),
                
                # 界面配置
                "THEME": config.get("theme", "default"),
                "LOG_LEVEL": config.get("log_level", "INFO"),
                "AUTO_SAVE": config.get("auto_save", True),
                "SANDBOX": False,
                
                # 策略启用状态
                "STRATEGIES_ENABLED": {
                    "rsi": config.get("rsi_strategy_enabled", False),
                    "macd": config.get("macd_strategy_enabled", False),
                    "kdj": config.get("kdj_strategy_enabled", False),
                    "bollinger": config.get("bollinger_strategy_enabled", False),
                    "ema": config.get("ema_strategy_enabled", False),
                    "advanced_macd": config.get("advanced_macd_strategy_enabled", False)
                },
                
                # AI配置
                "AI_CONFIG": config.get("ai_features", {
                    "sentiment_analysis": True,
                    "signal_enhancement": True,
                    "daily_cost_limit": 20.0,
                    "timeout": 5.0
                }),
                
                # Gate.io优化配置
                "GATE_IO_OPTIMIZATION": config.get("gate_io_optimization", {}),
                
                # 免费API源配置
                "FREE_API_SOURCES": config.get("free_api_sources", [])
            }
            
            # 保存到用户目录
            target_path = os.path.join(self.config_dir, "wmzc_config.json")
            with open(target_path, 'w', encoding='utf-8') as f:
                json.dump(standard_config, f, indent=2, ensure_ascii=False)
            
            print(f"✅ wmzc_config.json 已修复并保存到: {target_path}")
            self.fixes_applied += 1
            
        except Exception as e:
            print(f"❌ 修复 wmzc_config.json 失败: {e}")
            return False
        
        return True
    
    def fix_trading_config(self):
        """修复trading_config.json"""
        print("🔧 修复 trading_config.json...")
        
        try:
            # 加载现有配置
            with open(self.local_trading_config, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 清理和标准化配置
            clean_config = {
                # 基本交易配置
                "SYMBOL": config.get("SYMBOL", "BTC-USDT-SWAP"),
                "EXCHANGE": config.get("EXCHANGE", "OKX"),
                "TIMEFRAME": config.get("TIMEFRAME", "1m"),
                
                # API配置
                "API_KEY": config.get("API_KEY", "da636867-490f-4e3e-81b2-870841afb860"),
                "API_SECRET": config.get("API_SECRET", "C15B6EE0CF3FFDEE5834865D3839325E"),
                "PASSPHRASE": config.get("PASSPHRASE", "Mx123456@"),
                
                # OKX API配置
                "OKX_API_KEY": config.get("OKX_API_KEY", "da636867-490f-4e3e-81b2-870841afb860"),
                "OKX_SECRET_KEY": config.get("OKX_SECRET_KEY", "C15B6EE0CF3FFDEE5834865D3839325E"),
                "OKX_PASSPHRASE": config.get("OKX_PASSPHRASE", "Mx123456@"),
                
                # Gate.io API配置
                "GATE_API_KEY": config.get("GATE_API_KEY", "d5ea5faa068d66204bb68b75201c56d5"),
                "GATE_SECRET_KEY": config.get("GATE_SECRET_KEY", "5b516e55788fba27e61f9bd06b22ab3661b3115797076d5e73199bea3a8afb1c"),
                
                # 交易参数
                "ORDER_AMOUNT": config.get("ORDER_AMOUNT", 5.0),
                "ORDER_USDT_AMOUNT": config.get("ORDER_USDT_AMOUNT", 10.0),
                "LEVERAGE": config.get("LEVERAGE", 3),
                "RISK_PERCENT": config.get("RISK_PERCENT", 4.0),
                
                # 风险管理
                "ENABLE_STOP_LOSS": config.get("ENABLE_STOP_LOSS", True),
                "STOP_LOSS_PCT": config.get("STOP_LOSS_PCT", 1.0),
                "PROFIT_TARGET_PCT": config.get("PROFIT_TARGET_PCT", 2.0),
                
                # 系统配置
                "ENABLE_TRADING": config.get("ENABLE_TRADING", False),
                "TEST_MODE": config.get("TEST_MODE", True),
                
                # 技术指标配置
                "TECHNICAL_INDICATORS": config.get("TECHNICAL_INDICATORS", {}),
                
                # 策略配置
                "STRATEGY_MARKET": config.get("STRATEGY_MARKET", {}),
                
                # KDJ配置
                "KDJ": config.get("KDJ", {}),
                "ENABLE_KDJ": config.get("ENABLE_KDJ", True),
                
                # MACD配置
                "MACD": config.get("MACD", {}),
                "ENABLE_MACD": config.get("ENABLE_MACD", True),
                "ENABLE_ADVANCED_MACD": config.get("ENABLE_ADVANCED_MACD", False),
                "ADVANCED_MACD": config.get("ADVANCED_MACD", {}),
                
                # 插针策略配置
                "PINBAR": config.get("PINBAR", {}),
                "ENABLE_PINBAR": config.get("ENABLE_PINBAR", True),
                
                # RSI配置
                "RSI_STRATEGY": config.get("rsi_strategy", {}),
                
                # 风险管理配置
                "RISK_MANAGEMENT": config.get("risk_management", {}),
                
                # 等量加仓配置
                "EQUAL_POSITION": config.get("equal_position", {}),
                
                # AI配置
                "AI_CONFIG": config.get("ai_config", {}),
                
                # 推送配置
                "PUSH_CONFIG": {
                    "PUSH_MACD_SIGNAL": config.get("PUSH_MACD_SIGNAL", True),
                    "PUSH_KDJ_SIGNAL": config.get("PUSH_KDJ_SIGNAL", False),
                    "PUSH_PINBAR_SIGNAL": config.get("PUSH_PINBAR_SIGNAL", False),
                    "PUSH_RSI_SIGNAL": config.get("PUSH_RSI_SIGNAL", True),
                    "PUSH_BUY_SIGNAL": config.get("PUSH_BUY_SIGNAL", True),
                    "PUSH_SELL_SIGNAL": config.get("PUSH_SELL_SIGNAL", True),
                    "ENABLE_WECHAT_PUSH": config.get("ENABLE_WECHAT_PUSH", False),
                    "WECHAT_SENDKEY": config.get("WECHAT_SENDKEY", "")
                },
                
                # 界面配置
                "UI_CONFIG": {
                    "window_geometry": config.get("window_geometry", "1382x800+77+32"),
                    "current_tab": config.get("current_tab", 0),
                    "auto_refresh_news": config.get("auto_refresh_news", False),
                    "news_refresh_interval": config.get("news_refresh_interval", 3)
                },
                
                # 自动恢复配置
                "AUTO_RECOVERY": config.get("AUTO_RECOVERY", {
                    "enabled": True,
                    "timeout_minutes": 1,
                    "max_restarts": 3
                })
            }
            
            # 保存到用户目录
            target_path = os.path.join(self.config_dir, "trading_config.json")
            with open(target_path, 'w', encoding='utf-8') as f:
                json.dump(clean_config, f, indent=2, ensure_ascii=False)
            
            print(f"✅ trading_config.json 已修复并保存到: {target_path}")
            self.fixes_applied += 1
            
        except Exception as e:
            print(f"❌ 修复 trading_config.json 失败: {e}")
            return False
        
        return True
    
    def validate_configs(self):
        """验证修复后的配置"""
        print("🔍 验证修复后的配置...")
        
        try:
            # 验证wmzc_config.json
            wmzc_config_path = os.path.join(self.config_dir, "wmzc_config.json")
            with open(wmzc_config_path, 'r', encoding='utf-8') as f:
                wmzc_config = json.load(f)
            
            # 检查API密钥长度
            api_key = wmzc_config.get("API_KEY", "")
            passphrase = wmzc_config.get("PASSPHRASE", "")
            
            if len(api_key) >= 10:
                print("✅ API_KEY 长度验证通过")
            else:
                print(f"⚠️ API_KEY 长度不足: {len(api_key)} < 10")
            
            if len(passphrase) >= 3:
                print("✅ PASSPHRASE 长度验证通过")
            else:
                print(f"⚠️ PASSPHRASE 长度不足: {len(passphrase)} < 3")
            
            # 验证trading_config.json
            trading_config_path = os.path.join(self.config_dir, "trading_config.json")
            with open(trading_config_path, 'r', encoding='utf-8') as f:
                trading_config = json.load(f)
            
            print("✅ 配置文件JSON格式验证通过")
            return True
            
        except Exception as e:
            print(f"❌ 配置验证失败: {e}")
            return False
    
    def run_fix(self):
        """运行配置修复"""
        print("🚀 开始WMZC配置修复...")
        
        # 1. 创建配置目录
        if not self.create_config_directory():
            return False
        
        # 2. 备份现有配置
        if not self.backup_configs():
            return False
        
        # 3. 修复配置文件
        success = True
        success &= self.fix_wmzc_config()
        success &= self.fix_trading_config()
        
        if not success:
            return False
        
        # 4. 验证修复结果
        if not self.validate_configs():
            return False
        
        print(f"🎉 配置修复完成！共应用了 {self.fixes_applied} 个修复")
        print(f"📁 配置文件已保存到: {self.config_dir}")
        print("💡 重新启动系统以应用新配置")
        
        return True

def main():
    """主函数"""
    print("=" * 80)
    print("🔧 WMZC配置文件修复工具")
    print("解决配置警告：未知字段和API密钥长度验证问题")
    print("=" * 80)
    
    fixer = WMZCConfigFixer()
    
    try:
        success = fixer.run_fix()
        
        if success:
            print("\n✅ 配置修复成功！")
            print("📋 修复内容:")
            print("   ✅ 标准化了配置文件结构")
            print("   ✅ 解决了未知字段警告")
            print("   ✅ 修复了API密钥长度验证")
            print("   ✅ 整理了配置文件层次结构")
            print("\n💡 建议重新启动系统以应用新配置")
            return True
        else:
            print("\n❌ 配置修复失败")
            return False
            
    except Exception as e:
        print(f"\n💥 修复异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
