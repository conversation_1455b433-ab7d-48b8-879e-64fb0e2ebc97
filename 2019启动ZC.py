﻿#!/usr/bin/env python3
# -*- coding: utf-8 -*-


#☆————————————女神保佑，代码无bug—————————————☆
#☆                                                                     ☆
#☆                                                                     ☆
#☆                       .::::.                                        ☆
#☆                     .::::::::.                                      ☆
#☆                    :::::::::::                                      ☆
#☆                 ..:::::::::::'                                      ☆
#☆              '::::::::::::'                                         ☆
#☆                .::::::::::                                          ☆
#☆           '::::::::::::::..                                         ☆
#☆                ..::::::::::::.                                      ☆
#☆              ``###########omOoo                                     ☆
#☆               ::::``xyq3.comO'        .:::.                         ☆
#☆              ::::'   'XYq3.'       .::::::::.                       ☆
#☆            .::::'      COM.     .:::::::'::::.                      ☆
#☆           .:::'       :::::  .:::::::::' ':::::.                    ☆
#☆          .::'        :::::.:::::::::'      ':::::.                  ☆
#☆         .::'         ::::::::::::::'         ``::::.                ☆
#☆     ...:::           ::::::::::::'              ``::.               ☆
#☆    ````':.          ':::::::::'                  ::::..             ☆
#☆                       '.:::::'                    ':'````..         ☆
#☆                                                                     ☆
#☆---------------------------------------------------------------------☆

"""
直接启动GUI - 最简单的方式
"""

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 OKX交易系统 - 直接GUI启动")
    print("=" * 60)
    
    try:
        # 导入主系统
        print("📦 导入主系统...")
        import WMZC
        print("✅ 主系统导入成功")

        # 初始化系统组件
        print("🔧 初始化系统组件...")

        # 初始化全局变量
        print("  初始化全局变量...")
        if hasattr(WMZC, 'init_global_variables'):
            WMZC.init_global_variables()
        else:
            print("  ⚠️ init_global_variables函数不存在，跳过")

        # 初始化配置管理器
        print("  初始化配置管理器...")
        if hasattr(WMZC, 'init_config_manager'):
            config_result = WMZC.init_config_manager()
            if config_result:
                print("  ✅ 配置管理器初始化成功")
            else:
                print("  ⚠️ 配置管理器初始化失败，使用默认配置")
        else:
            print("  ⚠️ init_config_manager函数不存在，跳过")

        # 初始化监控系统
        print("  初始化监控系统...")
        if hasattr(WMZC, 'init_monitoring_system'):
            monitor_result = WMZC.init_monitoring_system()
            if monitor_result:
                print("  ✅ 监控系统初始化成功")
            else:
                print("  ⚠️ 监控系统初始化失败，使用简化版本")
        else:
            print("  ⚠️ init_monitoring_system函数不存在，跳过")

        # 创建并启动GUI
        print("\n🖥️ 创建GUI界面...")
        app = WMZC.TradingApp()
        print("✅ GUI创建成功")
        
        # 显示窗口信息
        print(f"窗口标题: {app.title()}")
        
        # 检查标签页
        if hasattr(app, 'tabs'):
            tab_names = list(app.tabs.keys())
            print(f"标签页数量: {len(tab_names)}")
            print("可用功能:")
            for i, name in enumerate(tab_names, 1):
                print(f"  {i}. {name}")
        
        print("\n🎉 系统准备完成！")
        print("=" * 60)
        print("🖥️ 启动GUI主循环...")
        print("💡 关闭窗口即可退出系统")
        print("=" * 60)
        
        # 启动GUI主循环
        app.mainloop()
        
        print("\n👋 交易系统已关闭")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
