#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WMZC配置修复工具
修复API密钥配置和配置字段问题
"""

import json
import os
import shutil
from pathlib import Path

def fix_config_issues():
    """修复配置问题"""
    print("🔧 开始修复WMZC配置问题...")
    print("=" * 50)
    
    # 配置文件路径
    config_dir = Path.home() / ".wmzc_trading"
    config_files = [
        "wmzc_config.json",
        "trading_config.json",
        "user_settings.json",
        "misc_optimization_config.json",
        "ai_config.json"
    ]
    
    # 确保配置目录存在
    config_dir.mkdir(exist_ok=True)
    
    # 标准配置模板
    standard_config = {
        "API_KEY": "demo_api_key_12345678901234567890",
        "SECRET_KEY": "demo_secret_key_12345678901234567890",
        "PASSPHRASE": "demo_passphrase",
        "SANDBOX": True,
        "EXCHANGE": "OKX",
        "DEFAULT_SYMBOL": "BTC-USDT-SWAP",
        "DEFAULT_TIMEFRAME": "1m",
        "LOG_LEVEL": "INFO",
        "THEME": "default",
        "AUTO_SAVE": True,
        "AUTO_TRADING": False,
        "MAX_POSITION_SIZE": 0.1,
        "RISK_PER_TRADE": 0.02,
        "MAX_DAILY_TRADES": 10,
        "ENABLE_STOP_LOSS": True,
        "STOP_LOSS_PCT": 1.0,
        "ENABLE_TAKE_PROFIT": True,
        "TAKE_PROFIT_PCT": 2.0,
        "INDICATOR_SYNC": {
            "KDJ": {
                "sync_enabled": False,
                "LOW": -2,
                "HIGH": 90,
                "window": 14
            },
            "MACD": {
                "sync_enabled": False,
                "fast": 12,
                "slow": 26,
                "signal": 9
            }
        },
        "KDJ": {
            "LOW": -2,
            "HIGH": 90,
            "window": 14
        },
        "MACD": {
            "fast": 12,
            "slow": 26,
            "signal": 9
        },
        "RSI": {
            "period": 14,
            "overbought": 70,
            "oversold": 30
        },
        "PINBAR": {
            "DROP_TIME_WINDOW": 2,
            "DROP_PERCENT": 5,
            "REBOUND_PERCENT": 1,
            "RISE_TIME_WINDOW": 2,
            "RISE_PERCENT": 5,
            "FALL_PERCENT": 1,
            "TAKE_PROFIT_PCT": 50
        },
        "strategies": {
            "macd": {
                "enabled": False,
                "weight": 1.0
            },
            "rsi": {
                "enabled": False,
                "weight": 0.8
            },
            "kdj": {
                "enabled": False,
                "weight": 0.7
            }
        },
        "risk": {
            "max_position_size": 0.1,
            "max_daily_loss": 0.05,
            "max_drawdown": 0.2
        },
        "ai_config": {
            "enabled": False,
            "model": "basic",
            "confidence_threshold": 0.7
        }
    }
    
    fixed_count = 0
    
    for config_file in config_files:
        config_path = config_dir / config_file
        
        print(f"\n🔧 处理配置文件: {config_file}")
        
        try:
            # 读取现有配置
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    existing_config = json.load(f)
                print(f"  📖 已读取现有配置")
            else:
                existing_config = {}
                print(f"  📝 创建新配置文件")
            
            # 创建备份
            if config_path.exists():
                backup_path = config_path.with_suffix('.json.backup')
                shutil.copy2(config_path, backup_path)
                print(f"  💾 已创建备份: {backup_path.name}")
            
            # 合并配置（保留现有有效配置，添加缺失的标准配置）
            merged_config = standard_config.copy()
            
            # 保留现有的有效配置
            for key, value in existing_config.items():
                if key in ['API_KEY', 'SECRET_KEY', 'PASSPHRASE']:
                    # 只有当现有值有效时才保留
                    if isinstance(value, str) and len(value) >= 10:
                        merged_config[key] = value
                else:
                    merged_config[key] = value
            
            # 确保API密钥字段长度符合要求
            if len(merged_config.get('API_KEY', '')) < 10:
                merged_config['API_KEY'] = 'demo_api_key_12345678901234567890'
                print(f"  🔑 已设置演示API密钥")
            
            if len(merged_config.get('PASSPHRASE', '')) < 3:
                merged_config['PASSPHRASE'] = 'demo_passphrase'
                print(f"  🔑 已设置演示密码短语")
            
            # 保存修复后的配置
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(merged_config, f, indent=2, ensure_ascii=False)
            
            print(f"  ✅ 配置文件已修复")
            fixed_count += 1
            
        except Exception as e:
            print(f"  ❌ 修复失败: {e}")
    
    print(f"\n" + "=" * 50)
    print(f"🎉 配置修复完成!")
    print(f"✅ 已修复 {fixed_count}/{len(config_files)} 个配置文件")
    
    # 显示配置说明
    print(f"\n💡 配置说明:")
    print(f"  📁 配置目录: {config_dir}")
    print(f"  🔑 当前使用演示密钥（沙盒模式）")
    print(f"  ⚠️  如需实盘交易，请在GUI中配置真实API密钥")
    print(f"  🛡️  建议先在沙盒环境中测试系统功能")
    
    return True

if __name__ == "__main__":
    fix_config_issues()
