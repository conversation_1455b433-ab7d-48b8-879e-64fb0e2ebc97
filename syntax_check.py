#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WMZC.py语法检查器
"""

import ast
import sys

def check_syntax():
    """检查WMZC.py的语法"""
    try:
        with open('WMZC.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 尝试解析语法
        ast.parse(content)
        print('✅ 语法检查通过：无语法错误')
        return True
        
    except SyntaxError as e:
        print(f'❌ 语法错误: {e.msg}')
        print(f'   位置: 第{e.lineno}行, 第{e.offset}列')
        if e.text:
            print(f'   内容: {e.text.strip()}')
        return False
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')
        return False

def check_await_outside_async():
    """检查是否还有await在非async函数中的情况"""
    try:
        with open('WMZC.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        issues = []
        in_async_function = False
        current_function_line = 0
        
        for i, line in enumerate(lines, 1):
            line_stripped = line.strip()
            
            # 检查函数定义
            if line_stripped.startswith('def ') and 'async def' not in line:
                in_async_function = False
                current_function_line = i
            elif line_stripped.startswith('async def '):
                in_async_function = True
                current_function_line = i
            
            # 检查await使用
            if 'await ' in line and not in_async_function and not line_stripped.startswith('#'):
                issues.append({
                    'line': i,
                    'content': line_stripped,
                    'function_line': current_function_line
                })
        
        if issues:
            print(f'⚠️ 发现 {len(issues)} 个潜在的await使用问题:')
            for issue in issues:
                print(f'   第{issue["line"]}行: {issue["content"]}')
        else:
            print('✅ 未发现await在非async函数中的使用')
        
        return len(issues) == 0
        
    except Exception as e:
        print(f'❌ await检查失败: {e}')
        return False

if __name__ == "__main__":
    print("🔍 开始WMZC.py语法检查")
    print("=" * 40)
    
    syntax_ok = check_syntax()
    await_ok = check_await_outside_async()
    
    print("=" * 40)
    if syntax_ok and await_ok:
        print("🎉 所有检查通过！")
        sys.exit(0)
    else:
        print("❌ 发现问题，需要修复")
        sys.exit(1)
